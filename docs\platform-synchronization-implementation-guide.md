# Platform Synchronization Implementation Guide

## Overview

This document provides a comprehensive, step-by-step implementation guide for resolving platform inconsistencies, eliminating mock data, and ensuring synchronization between dashboard and virtual-community contexts.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Phase 1: Critical Issues (Weeks 1-3)](#phase-1-critical-issues-weeks-1-3)
3. [Phase 2: High Priority (Weeks 4-5)](#phase-2-high-priority-weeks-4-5)
4. [Phase 3: Optimization (Week 6)](#phase-3-optimization-week-6)
5. [Testing Strategy](#testing-strategy)
6. [Deployment Plan](#deployment-plan)

## Prerequisites

### Development Environment Setup
```bash
# Ensure you have the latest dependencies
npm install

# Run database migrations if needed
npm run db:migrate

# Start development server
npm run dev
```

### Required Knowledge
- Vue 3 Composition API
- TypeScript
- Supabase/PostgreSQL
- Pinia state management
- Real-time subscriptions

## Phase 1: Critical Issues (Weeks 1-3)

### Week 1: Replace Mock Content Matchmaking System

#### Step 1.1: Create Real Content Matching Service

**File**: `src/services/contentMatchingService.ts`

```typescript
import { supabase } from '@/lib/supabase'
import type { BaseProfile } from '@/types/profiles'

export interface ContentMatch {
  id: string
  entityId: string
  entityType: 'post' | 'event' | 'marketplace' | 'group'
  score: number
  reasons: Record<string, number>
  content: any
}

export function useContentMatchingService() {
  async function getContentMatches(
    userProfile: BaseProfile,
    contentType: string = 'all',
    limit: number = 10
  ): Promise<ContentMatch[]> {
    // Implementation details in next steps
  }
}
```

#### Step 1.2: Implement Matching Algorithms

**Day 1-2**: Create scoring algorithms
```typescript
// Add to contentMatchingService.ts
function calculateContentScore(
  userProfile: BaseProfile,
  content: any,
  contentType: string
): { score: number; reasons: Record<string, number> } {
  const reasons: Record<string, number> = {}
  let totalScore = 0

  // Industry alignment (30% weight)
  if (userProfile.industry && content.industry) {
    const industryMatch = calculateIndustryMatch(userProfile.industry, content.industry)
    reasons.industry_match = industryMatch
    totalScore += industryMatch * 0.3
  }

  // Interest alignment (25% weight)
  if (userProfile.interests && content.tags) {
    const interestMatch = calculateInterestMatch(userProfile.interests, content.tags)
    reasons.interest_match = interestMatch
    totalScore += interestMatch * 0.25
  }

  // Location relevance (20% weight)
  if (userProfile.location && content.location) {
    const locationMatch = calculateLocationMatch(userProfile.location, content.location)
    reasons.location_match = locationMatch
    totalScore += locationMatch * 0.2
  }

  // Recency (15% weight)
  const recencyScore = calculateRecencyScore(content.created_at)
  reasons.recency = recencyScore
  totalScore += recencyScore * 0.15

  // Popularity (10% weight)
  const popularityScore = calculatePopularityScore(content)
  reasons.popularity = popularityScore
  totalScore += popularityScore * 0.1

  return { score: Math.min(totalScore, 1), reasons }
}
```

**Day 3-4**: Implement content fetching
```typescript
// Add to contentMatchingService.ts
async function fetchContentByType(contentType: string): Promise<any[]> {
  switch (contentType) {
    case 'post':
      return await fetchPosts()
    case 'event':
      return await fetchEvents()
    case 'marketplace':
      return await fetchMarketplaceItems()
    case 'group':
      return await fetchGroups()
    default:
      return await fetchAllContent()
  }
}

async function fetchPosts(): Promise<any[]> {
  const { data, error } = await supabase
    .from('posts_with_authors')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(100)

  if (error) throw error
  return data || []
}
```

#### Step 1.3: Update ContentMatchmaking Component

**File**: `src/views/dashboard/ContentMatchmaking.vue`

**Day 5**: Replace mock implementation
```typescript
// Replace the simulateContentMatches function
async function generateContentMatches() {
  try {
    loading.value = true
    error.value = null

    // Get user profile
    const userProfile = profileStore.currentProfile
    if (!userProfile) {
      throw new Error('User profile not found')
    }

    // Use real content matching service
    const contentMatchingService = useContentMatchingService()
    const matches = await contentMatchingService.getContentMatches(
      userProfile,
      selectedContentType.value,
      20 // Get more matches to filter by score
    )

    // Filter by minimum score
    contentMatches.value = matches
      .filter(match => match.score >= minScore.value)
      .sort((a, b) => b.score - a.score)
      .slice(0, 10) // Limit to top 10

    hasMoreMatches.value = matches.length > 10
  } catch (err: any) {
    console.error('Error generating content matches:', err)
    error.value = err.message || 'Failed to generate content matches'
  } finally {
    loading.value = false
  }
}
```

### Week 2: Fix Hardcoded Network Statistics

#### Step 2.1: Create Profile Statistics Service

**File**: `src/services/profileStatsService.ts`

```typescript
import { supabase } from '@/lib/supabase'
import { ref, computed } from 'vue'

export interface UserStats {
  posts: number
  events: number
  groups: number
  connections: number
  likes_received: number
  comments_received: number
}

export function useProfileStatsService() {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const statsCache = ref<Map<string, { stats: UserStats; timestamp: number }>>(new Map())

  async function getUserStats(userId: string, forceRefresh = false): Promise<UserStats> {
    // Check cache first (5 minute TTL)
    const cached = statsCache.value.get(userId)
    const now = Date.now()
    if (!forceRefresh && cached && (now - cached.timestamp) < 300000) {
      return cached.stats
    }

    try {
      loading.value = true
      error.value = null

      const [postsCount, eventsCount, groupsCount, connectionsCount, likesCount, commentsCount] = 
        await Promise.all([
          getUserPostCount(userId),
          getUserEventCount(userId),
          getUserGroupCount(userId),
          getUserConnectionCount(userId),
          getUserLikesCount(userId),
          getUserCommentsCount(userId)
        ])

      const stats: UserStats = {
        posts: postsCount,
        events: eventsCount,
        groups: groupsCount,
        connections: connectionsCount,
        likes_received: likesCount,
        comments_received: commentsCount
      }

      // Cache the results
      statsCache.value.set(userId, { stats, timestamp: now })

      return stats
    } catch (err: any) {
      error.value = err.message || 'Failed to load user statistics'
      throw err
    } finally {
      loading.value = false
    }
  }

  return {
    getUserStats,
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    clearCache: () => statsCache.value.clear()
  }
}
```

#### Step 2.2: Implement Individual Count Functions

```typescript
// Add to profileStatsService.ts
async function getUserPostCount(userId: string): Promise<number> {
  const { count, error } = await supabase
    .from('posts')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId)

  if (error) throw error
  return count || 0
}

async function getUserEventCount(userId: string): Promise<number> {
  const { count, error } = await supabase
    .from('posts')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId)
    .eq('post_type', 'event')

  if (error) throw error
  return count || 0
}

async function getUserGroupCount(userId: string): Promise<number> {
  // Assuming we have a user_groups table
  const { count, error } = await supabase
    .from('user_groups')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId)

  if (error) {
    console.warn('user_groups table not found, returning 0')
    return 0
  }
  return count || 0
}

async function getUserConnectionCount(userId: string): Promise<number> {
  const { count, error } = await supabase
    .from('user_connections')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId)
    .eq('connection_status', 'accepted')

  if (error) {
    console.warn('user_connections table not found, returning 0')
    return 0
  }
  return count || 0
}
```

#### Step 2.3: Update UnifiedProfileView Component

**File**: `src/components/profile/UnifiedProfileView.vue`

```vue
<template>
  <!-- Network Stats (for public context) -->
  <q-card v-if="context === 'public'" class="q-mb-md section-card">
    <q-card-section class="section-header bg-primary text-white">
      <div class="text-h6">
        <unified-icon name="people" class="q-mr-sm" />
        Network
      </div>
    </q-card-section>
    <q-card-section>
      <div v-if="loadingStats" class="text-center q-pa-md">
        <q-spinner color="primary" size="1.5em" />
        <div class="text-caption q-mt-sm">Loading statistics...</div>
      </div>
      <div v-else class="row q-mt-sm">
        <div class="col-6 col-md-3 q-pa-sm text-center">
          <div class="text-h5">{{ userStats?.connections || 0 }}</div>
          <div class="text-caption">Connections</div>
        </div>
        <div class="col-6 col-md-3 q-pa-sm text-center">
          <div class="text-h5">{{ userStats?.posts || 0 }}</div>
          <div class="text-caption">Posts</div>
        </div>
        <div class="col-6 col-md-3 q-pa-sm text-center">
          <div class="text-h5">{{ userStats?.events || 0 }}</div>
          <div class="text-caption">Events</div>
        </div>
        <div class="col-6 col-md-3 q-pa-sm text-center">
          <div class="text-h5">{{ userStats?.groups || 0 }}</div>
          <div class="text-caption">Groups</div>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { useProfileStatsService } from '@/services/profileStatsService'

// Add to existing script
const profileStatsService = useProfileStatsService()
const userStats = ref<UserStats | null>(null)
const loadingStats = ref(false)

// Load user statistics
async function loadUserStats() {
  if (!props.profileId) return
  
  try {
    loadingStats.value = true
    userStats.value = await profileStatsService.getUserStats(props.profileId)
  } catch (err) {
    console.error('Error loading user stats:', err)
  } finally {
    loadingStats.value = false
  }
}

// Load stats when profile loads
watch(() => combinedProfile.value, (newProfile) => {
  if (newProfile && props.context === 'public') {
    loadUserStats()
  }
}, { immediate: true })
</script>
```

### Week 3: Implement Real-time Counter Updates

#### Step 3.1: Create Real-time Counter Service

**File**: `src/services/realtimeCounterService.ts`

```typescript
import { supabase } from '@/lib/supabase'
import { ref, onUnmounted } from 'vue'

export function useRealtimeCounterService() {
  const subscriptions = ref<any[]>([])

  function subscribeToUserStats(userId: string, callback: (stats: any) => void) {
    // Subscribe to posts changes
    const postsSubscription = supabase
      .channel(`user-posts-${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'posts',
          filter: `user_id=eq.${userId}`
        },
        () => {
          // Recalculate and emit updated stats
          recalculateUserStats(userId, callback)
        }
      )
      .subscribe()

    // Subscribe to connections changes
    const connectionsSubscription = supabase
      .channel(`user-connections-${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_connections',
          filter: `user_id=eq.${userId}`
        },
        () => {
          recalculateUserStats(userId, callback)
        }
      )
      .subscribe()

    subscriptions.value.push(postsSubscription, connectionsSubscription)
  }

  async function recalculateUserStats(userId: string, callback: (stats: any) => void) {
    try {
      const profileStatsService = useProfileStatsService()
      const stats = await profileStatsService.getUserStats(userId, true) // Force refresh
      callback(stats)
    } catch (error) {
      console.error('Error recalculating user stats:', error)
    }
  }

  function cleanup() {
    subscriptions.value.forEach(subscription => {
      supabase.removeChannel(subscription)
    })
    subscriptions.value = []
  }

  onUnmounted(() => {
    cleanup()
  })

  return {
    subscribeToUserStats,
    cleanup
  }
}
```

## Phase 2: High Priority (Weeks 4-5)

### Week 4: Unify Message System Across Contexts

#### Step 4.1: Create Unified Message Components

**File**: `src/components/messaging/UnifiedMessageDialog.vue`

```vue
<template>
  <q-dialog v-model="isOpen" persistent>
    <q-card style="min-width: 400px; max-width: 600px;">
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">
          {{ isNewConversation ? 'New Message' : 'Conversation' }}
        </div>
        <q-space />
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <!-- Conversation history (if existing conversation) -->
      <q-card-section v-if="!isNewConversation" class="conversation-history">
        <div
          v-for="message in messages"
          :key="message.id"
          :class="['message', message.sender_id === currentUserId ? 'sent' : 'received']"
        >
          <div class="message-content">{{ message.content }}</div>
          <div class="message-time">{{ formatTime(message.created_at) }}</div>
        </div>
      </q-card-section>

      <!-- Message input -->
      <q-card-section>
        <q-input
          v-model="newMessage"
          type="textarea"
          :placeholder="isNewConversation ? 'Type your message...' : 'Reply...'"
          rows="3"
          outlined
          @keyup.ctrl.enter="sendMessage"
        />
      </q-card-section>

      <q-card-actions align="right">
        <q-btn flat label="Cancel" v-close-popup />
        <q-btn
          color="primary"
          label="Send"
          :loading="sending"
          @click="sendMessage"
          :disable="!newMessage.trim()"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>
```

### Week 5: Complete Feed Personalization

#### Step 5.1: Implement Feed Personalization Service

**File**: `src/services/feedPersonalizationService.ts`

```typescript
export function useFeedPersonalizationService() {
  async function getPersonalizedFeed(
    userId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<any[]> {
    // Get user profile and preferences
    const userProfile = await getUserProfile(userId)
    const userInteractions = await getUserInteractions(userId)
    
    // Fetch and score content
    const allContent = await fetchAllContent()
    const scoredContent = allContent.map(content => ({
      ...content,
      relevanceScore: calculateRelevanceScore(content, userProfile, userInteractions)
    }))
    
    // Sort by relevance and paginate
    return scoredContent
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice((page - 1) * limit, page * limit)
  }
}
```

## Phase 3: Optimization (Week 6)

### Week 6: Performance and Consistency Improvements

#### Step 6.1: Optimize Logging System

**File**: `src/utils/logger.ts`

```typescript
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

export class Logger {
  private static instance: Logger
  private logLevel: LogLevel = import.meta.env.PROD ? LogLevel.WARN : LogLevel.DEBUG

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }

  debug(message: string, ...args: any[]) {
    if (this.logLevel <= LogLevel.DEBUG) {
      console.log(`[DEBUG] ${message}`, ...args)
    }
  }

  info(message: string, ...args: any[]) {
    if (this.logLevel <= LogLevel.INFO) {
      console.info(`[INFO] ${message}`, ...args)
    }
  }

  warn(message: string, ...args: any[]) {
    if (this.logLevel <= LogLevel.WARN) {
      console.warn(`[WARN] ${message}`, ...args)
    }
  }

  error(message: string, ...args: any[]) {
    if (this.logLevel <= LogLevel.ERROR) {
      console.error(`[ERROR] ${message}`, ...args)
    }
  }
}

export const logger = Logger.getInstance()
```

## Testing Strategy

### Unit Tests
```typescript
// tests/services/profileStatsService.test.ts
import { describe, it, expect, vi } from 'vitest'
import { useProfileStatsService } from '@/services/profileStatsService'

describe('ProfileStatsService', () => {
  it('should calculate user stats correctly', async () => {
    const service = useProfileStatsService()
    const stats = await service.getUserStats('test-user-id')
    
    expect(stats).toHaveProperty('posts')
    expect(stats).toHaveProperty('connections')
    expect(typeof stats.posts).toBe('number')
  })
})
```

### Integration Tests
```typescript
// tests/integration/profileSync.test.ts
describe('Profile Synchronization', () => {
  it('should sync profile stats between dashboard and virtual-community', async () => {
    // Test cross-context synchronization
  })
})
```

## Deployment Plan

### Pre-deployment Checklist
- [ ] All unit tests passing
- [ ] Integration tests passing
- [ ] Performance benchmarks met
- [ ] Database migrations ready
- [ ] Feature flags configured

### Deployment Steps
1. **Database Migration**: Apply any required schema changes
2. **Feature Flag Rollout**: Gradually enable new features
3. **Monitoring**: Monitor performance and error rates
4. **Rollback Plan**: Ready to revert if issues arise

### Post-deployment Verification
- [ ] All counters showing real data
- [ ] Cross-context synchronization working
- [ ] Performance within acceptable limits
- [ ] No increase in error rates

## Success Metrics

- **Data Accuracy**: 100% real data, 0% mock data
- **Performance**: Page load times < 2 seconds
- **Synchronization**: 0 reported inconsistencies
- **User Experience**: 15% improvement in engagement metrics

## Additional Implementation Details

### Database Schema Updates

#### Required Tables and Indexes

```sql
-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_posts_user_id_created_at ON posts(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_connections_user_id_status ON user_connections(user_id, connection_status);
CREATE INDEX IF NOT EXISTS idx_user_activity_user_id_timestamp ON user_activity(user_id, timestamp DESC);

-- Add user_groups table if not exists
CREATE TABLE IF NOT EXISTS user_groups (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  group_id UUID NOT NULL,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  role TEXT DEFAULT 'member',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Environment Configuration

#### Development Environment
```bash
# .env.local
VITE_ENABLE_REAL_TIME_COUNTERS=true
VITE_ENABLE_CONTENT_MATCHING=true
VITE_LOG_LEVEL=debug
VITE_CACHE_TTL=300000
```

#### Production Environment
```bash
# .env.production
VITE_ENABLE_REAL_TIME_COUNTERS=true
VITE_ENABLE_CONTENT_MATCHING=true
VITE_LOG_LEVEL=warn
VITE_CACHE_TTL=600000
```

### Error Handling Patterns

#### Service Error Handling
```typescript
// Standard error handling pattern for all services
export function createServiceWithErrorHandling<T>(serviceName: string, serviceImpl: T): T {
  return new Proxy(serviceImpl as any, {
    get(target, prop) {
      const originalMethod = target[prop]
      if (typeof originalMethod === 'function') {
        return async function(...args: any[]) {
          try {
            return await originalMethod.apply(target, args)
          } catch (error) {
            logger.error(`${serviceName}.${String(prop)} failed:`, error)
            throw new ServiceError(`${serviceName} operation failed`, error)
          }
        }
      }
      return originalMethod
    }
  })
}
```

### Performance Monitoring

#### Key Metrics to Track
```typescript
// src/utils/performanceMonitor.ts
export class PerformanceMonitor {
  static trackOperation(operationName: string, operation: () => Promise<any>) {
    const startTime = performance.now()

    return operation().finally(() => {
      const duration = performance.now() - startTime
      logger.info(`Operation ${operationName} took ${duration.toFixed(2)}ms`)

      // Send to analytics if duration > threshold
      if (duration > 1000) {
        this.reportSlowOperation(operationName, duration)
      }
    })
  }
}
```

This implementation guide provides the detailed steps needed to systematically resolve all platform inconsistencies while maintaining stability and performance.
