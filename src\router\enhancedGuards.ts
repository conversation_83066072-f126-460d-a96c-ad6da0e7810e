import { Router, RouteLocationNormalized } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useProfileStore } from '@/stores/profile'
import { useUserState } from '@/services/userStateService'

/**
 * Enhanced route guards that use the centralized services for user state and profile loading
 */
export function setupEnhancedRouteGuards(router: Router) {
  router.beforeEach(async (to: RouteLocationNormalized, from: RouteLocationNormalized) => {
    const authStore = useAuthStore()
    const profileStore = useProfileStore()
    const { userState, isNewUser, hasIncompleteProfile, checkUserState } = useUserState()

    // Skip auth check for auth callback and verification routes
    if (to.path === '/auth/callback' || to.path === '/auth/verify') {
      return true
    }

    // Make sure auth is initialized before proceeding
    if (!authStore.isInitialized) {
      console.log('Auth not initialized, initializing...')
      await authStore.checkSession()
    }

    // Check if route requires authentication
    if (to.matched.some(record => record.meta.requiresAuth)) {
      if (!authStore.isAuthenticated) {
        console.log('Route requires auth but user is not authenticated, redirecting to sign-in')
        return '/sign-in'
      }

      // Only check user state if we don't have it cached or if it's been more than 5 minutes
      const lastStateCheck = sessionStorage.getItem('lastUserStateCheck')
      const now = Date.now()
      const fiveMinutes = 5 * 60 * 1000

      if (!lastStateCheck || (now - parseInt(lastStateCheck)) > fiveMinutes) {
        try {
          await checkUserState()
          sessionStorage.setItem('lastUserStateCheck', now.toString())
        } catch (error) {
          console.error('Error checking user state:', error)
          // Continue anyway, as we don't want to block navigation due to errors
        }
      }

      // Redirect based on user state if needed
      if (to.matched.some(record => record.meta.requiresCompleteProfile) &&
          hasIncompleteProfile.value) {
        console.log('Route requires complete profile but user has incomplete profile, redirecting to profile dashboard')
        return '/dashboard/profile'
      }

      // Redirect new users to profile creation
      if (to.matched.some(record => record.meta.requiresProfile) &&
          isNewUser.value) {
        console.log('Route requires profile but user is new, redirecting to profile creation')
        return '/dashboard/profile/create'
      }


    }

    // Check if route is guest-only (like sign-in)
    if (to.matched.some(record => record.meta.guestOnly)) {
      if (authStore.isAuthenticated) {
        console.log('Route is guest-only but user is authenticated, redirecting to dashboard')
        return '/dashboard'
      }
    }

    // Check if route requires no profile
    if (to.matched.some(record => record.meta.requiresNoProfile)) {
      if (!isNewUser.value) {
        console.log('Route requires no profile but user has a profile, redirecting to dashboard')
        return '/dashboard'
      }
    }

    return true
  })

  // After each navigation, scroll to top
  router.afterEach((to, from) => {
    // Scroll to top only if the route path changed
    if (to.path !== from.path) {
      window.scrollTo(0, 0)
    }
  })
}